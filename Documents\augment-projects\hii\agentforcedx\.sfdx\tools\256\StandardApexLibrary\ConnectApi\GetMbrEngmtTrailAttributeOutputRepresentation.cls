global class GetMbrEngmtTrailAttributeOutputRepresentation {
	global List<ConnectApi.MbrEngmtAttributeOutputRepresentation> memberEngagementAttributeOutputRepresentations;
	global String message;
	global String promotionName;
	global Integer remainingResetCount;
	global List<ConnectApi.RewardsOutputRepresentation> rewards;
	global Boolean status;
	global Integer totalResetCount;
	global GetMbrEngmtTrailAttributeOutputRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}