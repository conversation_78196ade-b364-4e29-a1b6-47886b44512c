global class GroupMemberPage {
	global String currentPageUrl;
	global List<ConnectApi.GroupMember> members;
	global ConnectApi.Reference myMembership;
	global String nextPageUrl;
	global String previousPageUrl;
	global Integer totalMemberCount;
	global GroupMemberPage() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}