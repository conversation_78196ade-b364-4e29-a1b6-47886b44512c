global class CommentCapabilitiesInput {
	global ConnectApi.ContentCapabilityInput content;
	global ConnectApi.ExtensionsCapabilityInput extensions;
	global ConnectApi.FeedEntityShareCapabilityInput feedEntityShare;
	global ConnectApi.RecordCapabilityInput record;
	global CommentCapabilitiesInput() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Integer hashCode() { }
	global String toString() { }

}