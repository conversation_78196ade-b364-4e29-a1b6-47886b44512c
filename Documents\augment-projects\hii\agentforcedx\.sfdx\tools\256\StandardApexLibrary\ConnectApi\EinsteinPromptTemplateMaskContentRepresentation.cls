global class EinsteinPromptTemplateMaskContentRepresentation {
	global String content;
	global ConnectApi.EinsteinPromptTemplateMaskSettingsRepresentation moderationSettings;
	global String role;
	global EinsteinPromptTemplateMaskContentRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}