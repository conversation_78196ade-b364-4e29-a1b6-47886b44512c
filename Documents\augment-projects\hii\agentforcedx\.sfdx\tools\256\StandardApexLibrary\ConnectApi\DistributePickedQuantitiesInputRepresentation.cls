global class DistributePickedQuantitiesInputRepresentation {
	global List<ConnectApi.DistributeToOrdersInputRepresentation> distributeToOrders;
	global String optimizationCriteria;
	global List<ConnectApi.ItemQuantityInputRepresentation> quantitiesPickedList;
	global DistributePickedQuantitiesInputRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Integer hashCode() { }
	global String toString() { }

}