global class CdpIdentityResolutionReconciliationFieldRule {
	global String fieldName;
	global ConnectApi.CdpIdentityResolutionReconciliationRuleType ruleType;
	global Boolean shouldIgnoreEmptyValue;
	global List<ConnectApi.CdpIdentityResolutionReconciliationSource> sources;
	global CdpIdentityResolutionReconciliationFieldRule() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Integer hashCode() { }
	global String toString() { }

}