global class CartInventoryItemReservationOutputRepresentation {
	global String errorCode;
	global String errorMessage;
	global String itemReservationSourceId;
	global String productId;
	global Double quantity;
	global String reservedAtLocationId;
	global CartInventoryItemReservationOutputRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}