global class RecordAggregationEntityApplicableFieldsOutputRepresentation {
	global List<ConnectApi.RecordAggregationApplicableFieldOutputRepresentation> fields;
	global String message;
	global String statusCode;
	global Map<String,List<String>> validOperators;
	global RecordAggregationEntityApplicableFieldsOutputRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}