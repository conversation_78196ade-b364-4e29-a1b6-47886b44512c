global class ProductCartItemCollection {
	global Integer count;
	global Integer currentPage;
	global Boolean hasErrors;
	global List<ConnectApi.ProductCartItem> products;
	global Integer totalItemCount;
	global Integer totalNumberOfPages;
	global ProductCartItemCollection() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}