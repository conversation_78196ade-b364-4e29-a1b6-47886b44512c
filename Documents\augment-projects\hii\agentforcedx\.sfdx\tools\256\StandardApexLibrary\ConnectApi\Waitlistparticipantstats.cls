global class WaitlistParticipantStats {
	global String errorMessage;
	global Integer positionInWaitlist;
	global String serviceAppointmentId;
	global ConnectApi.WaitlistResult waitlist;
	global String waitlistParticipantId;
	global ConnectApi.WorkTypeRepresentation workType;
	global ConnectApi.WorkTypeGroupRepresentation workTypeGroup;
	global WaitlistParticipantStats() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}