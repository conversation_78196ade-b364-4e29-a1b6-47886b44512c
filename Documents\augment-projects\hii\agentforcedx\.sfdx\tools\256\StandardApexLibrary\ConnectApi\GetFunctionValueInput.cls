global class GetFunctionValueInput {
	global List<ConnectApi.WrappedObject> arguments;
	global String contractAddress;
	global String functionName;
	global List<String> solidityInputTypes;
	global List<String> solidityOutputTypes;
	global String url;
	global GetFunctionValueInput() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Integer hashCode() { }
	global String toString() { }

}