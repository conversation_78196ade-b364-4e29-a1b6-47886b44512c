global enum ApiResponseStatusCode {
AT<PERSON>IB<PERSON><PERSON><PERSON>FOUND,
<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>SUCCESSFULLY,
CA<PERSON><PERSON><PERSON><PERSON>CCESSFULLYADDED,
CARTITEMSMISSINGPRICINGINFOERROR,
<PERSON>RT<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
CA<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>RO<PERSON>,
<PERSON><PERSON><PERSON><PERSON><PERSON>NOC<PERSON><PERSON>XTERROR,
<PERSON><PERSON><PERSON><PERSON><PERSON>UNKNOWNERROR,
CA<PERSON><PERSON><PERSON><PERSON><PERSON>ERROR,
CA<PERSON><PERSON><PERSON><PERSON>SFULLYCREATED,
CARTSUCCESSFULLYUPDATED,
CA<PERSON><PERSON>DATEERROR,
CA<PERSON><PERSON>EM<PERSON><PERSON><PERSON>OUNDERROR,
CATEGORYQUALIFICATIONFAILED,
CHATCO<PERSON>EXTIDEXPIRED,
CO<PERSON><PERSON><PERSON><PERSON>TE<PERSON>,
D<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>TERROR,
DUP<PERSON><PERSON>ATEFAVNAME,
<PERSON><PERSON><PERSON>EDDETAILSSUCCESSFULLY,
GUIDEDSELECTIONRESPONSERETRIEVALFAILED,
INTERNALSERVERERROR,
INVALIDACCOUNTID,
INVALIDADDI<PERSON><PERSON><PERSON>FIELDS,
INVALIDCARTID,
INVALIDCATALOGID,
INVALIDCATEGORYID,
INVALIDCONFIGURATION,
INVALIDCONTACTID,
INVALIDDEPTH,
INVALIDFAVORITEID,
INVALIDFILTER,
INVALIDFILTEROPERATOR,
INVALIDINPUTATTRIBUTEERROR,
INVALIDINPUTQUANTIYERROR,
INVALIDOFFSET,
INVALIDPAGESIZE,
INVALIDPARENTCATEGORYID,
INVALIDPRICEBOOKID,
INVALIDPRODUCTCLASSID,
INVALIDPRODUCTCONFIG,
INVALIDPRODUCTID,
INVALIDPRODUCTSELLINGMODELID,
INVALIDREFERENCERECORDID,
INVALIDSOBJECT,
INVALIDSEARCHSTRING,
INVALIDSORTDIRECTION,
INVALIDUSERID,
MAXQUANTITYVALIDATIONERROR,
MINQUANTITYVALIDATIONERROR,
MISSINGCARTID,
MISSINGCARTITEMID,
MISSINGCATALOGID,
MISSINGCATEGORYID,
MISSINGFAVORITEDATA,
MISSINGFAVORITEDESCRIPTION,
MISSINGFAVORITEID,
MISSINGFAVORITENAME,
MISSINGFAVORITEREFERENCERECORDID,
MISSINGFAVORITEUSERID,
MISSINGINPUTPARAMACTIONS,
MISSINGORDERNAME,
MISSINGORDERSUBMISSIONDATE,
MISSINGPRICEBOOKID,
MISSINGPRICINGINFORMATION,
MISSINGPRODUCTID,
MISSINGREFERENCERECORDID,
MISSINGSEARCHTERMORGUIDEDSELECTIONRESPONSEID,
MISSINGUSERCONTEXT,
MISSINGUSERQUERYINSEARCHPARAM,
NOCATALOGDETAILS,
NOCATEGORYDETAILS,
NOCATEGORYLIST,
NOPRODUCTFOUND,
NOPRODUCTIDS,
NOVALIDACTIONSPECIFIED,
NOVALIDATIONFOUND,
ORDERNOTFOUNDERROR,
ORDERPERSISTENCEERROR,
ORDERRETRIEVALERROR,
ORDERSUCCESSFULLYCREATED,
ORDERUPDATEERROR,
PARTIALPRODUCTSLIST,
PRODUCTDISCOVERYCONTEXTFAILED,
PRODUCTIDSABOVELIMIT,
PRODUCTPRICINGCONTEXTFAILED,
PRODUCTPRICINGFAILED,
PRODUCTPRICINGWITHWARNING,
PRODUCTQUALIFICATIONFAILED,
PRODUCTQUALIFICATIONWITHWARNING,
RELATEDOBJECTFILTERS,
RELATEDOBJECTFILTERSCRITERIALIMITEXCEEDED,
RELATEDOBJECTFILTERSLIMITEXCEEDED,
RELATEDOBJECTFILTERSOPERATORNOTSUPPORTED,
RELATEDOBJECTFILTERSPROPERTYNOTSUPPORTED,
RELATEDOBJECTFILTERSUNSUPPORTEDOBJECTNAME,
RELATEDOBJECTFILTERSVALUENOTSUPPORTED,
REQUIREDATTRIBUTEERROR,
UNABLETOADDTOCART,
UNABLETOCREATECART,
UNKNOWNERROR
}