global class RADFieldFilterOutputRepresentation {
	global String fieldApiName;
	global String fieldLabel;
	global String operator;
	global String operatorLabel;
	global String recordId;
	global Integer sequence;
	global String value;
	global RADFieldFilterOutputRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}