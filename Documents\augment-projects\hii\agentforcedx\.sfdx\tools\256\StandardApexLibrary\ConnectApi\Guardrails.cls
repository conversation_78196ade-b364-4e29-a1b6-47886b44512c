global class Guardrails {
	global String currentValue;
	global ConnectApi.GuardrailCurrentValue currentValues;
	global ConnectApi.GuardrailTypeEnumRepresentation guardrailType;
	global String limitValue;
	global Boolean multiValue;
	global String name;
	global Boolean notificationSupported;
	global Guardrails() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}