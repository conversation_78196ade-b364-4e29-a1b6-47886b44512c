global class EngagementTopicCreateInput {
	global List<ConnectApi.EngagementCustomFieldsInput> customFieldsList;
	global String engagementInteractionId;
	global String id;
	global String interactionSummary;
	global String name;
	global String ownerId;
	global String parentTopicId;
	global String processFailureReason;
	global String processName;
	global String processStatus;
	global String processType;
	global String relatedPersonId;
	global String topicId;
	global EngagementTopicCreateInput() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Integer hashCode() { }
	global String toString() { }

}