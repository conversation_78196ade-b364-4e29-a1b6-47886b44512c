global class ReturnItemsOutputRepresentation {
	global String changeOrderId;
	global String feeChangeOrderId;
	global List<ConnectApi.ReturnOrderItemSplitLineOutputRepresentation> returnLineItemSplits;
	global ReturnItemsOutputRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}