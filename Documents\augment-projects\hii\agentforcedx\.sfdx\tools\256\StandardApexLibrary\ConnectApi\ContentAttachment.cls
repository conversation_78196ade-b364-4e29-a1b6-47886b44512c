global class ContentAttachment {
	global String checksum;
	global String contentUrl;
	global String description;
	global String downloadUrl;
	global String fileExtension;
	global String fileSize;
	global String fileType;
	global Boolean hasImagePreview;
	global Boolean hasPdfPreview;
	global String id;
	global Boolean isInMyFileSync;
	global String mimeType;
	global String renditionUrl;
	global String renditionUrl240By180;
	global String renditionUrl720By480;
	global String textPreview;
	global String thumb120By90RenditionStatus;
	global String thumb240By180RenditionStatus;
	global String thumb720By480RenditionStatus;
	global String title;
	global String versionId;
	global ContentAttachment() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}