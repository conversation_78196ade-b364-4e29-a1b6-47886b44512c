global class ProductAttributeInfo {
	global List<String> allowableValues;
	global String apiName;
	global List<String> availableValues;
	global String fieldEnumOrId;
	global Boolean groupedBy;
	global String label;
	global String objectName;
	global List<ConnectApi.ProductAttributeValueMetadataRepresentation> options;
	global Integer sequence;
	global ConnectApi.ProductAttributeViewType viewType;
	global ProductAttributeInfo() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}