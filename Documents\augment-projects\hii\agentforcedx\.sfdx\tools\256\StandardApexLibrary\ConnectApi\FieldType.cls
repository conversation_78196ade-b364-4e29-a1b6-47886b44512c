global enum FieldType {
ADDRES<PERSON>,
<PERSON><PERSON><PERSON><PERSON><PERSON>,
BASE<PERSON>,
<PERSON><PERSON><PERSON><PERSON><PERSON>,
<PERSON><PERSON><PERSON>BO<PERSON>,
CO<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
<PERSON><PERSON><PERSON><PERSON><PERSON>,
<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>REFERENCE,
DATE,
DATETIME,
DOUBLE,
EMAIL,
ENCRYP<PERSON><PERSON><PERSON>ING,
<PERSON>XTEN<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
FL<PERSON><PERSON><PERSON><PERSON><PERSON>,
ID,
I<PERSON><PERSON><PERSON><PERSON>,
IN<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
INTEGER,
JSON,
L<PERSON><PERSON><PERSON>,
L<PERSON><PERSON>,
M<PERSON><PERSON><PERSON><PERSON>KLIST,
PERCENT,
PER<PERSON><PERSON><PERSON><PERSON>,
PHON<PERSON>,
PICKLIST,
PLAINTEXTAR<PERSON>,
REFERENCE,
RIC<PERSON>EXTAREA,
SOBJECT,
STRING,
<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>SONNA<PERSON>,
TEXTAREA,
TIME,
URL
}