global class ProductMedia {
	global String alternateText;
	global String contentVersionId;
	global String id;
	global ConnectApi.ProductMediaType mediaType;
	global Integer sortOrder;
	global String thumbnailUrl;
	global String title;
	global String url;
	global ProductMedia() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}