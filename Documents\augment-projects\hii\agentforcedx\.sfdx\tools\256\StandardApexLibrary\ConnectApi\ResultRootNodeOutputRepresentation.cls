global class ResultRootNodeOutputRepresentation {
	global List<ConnectApi.ResultNodeOutputRepresentation> children;
	global Map<String,Object> fields;
	global String name;
	global String type;
	global ResultRootNodeOutputRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}