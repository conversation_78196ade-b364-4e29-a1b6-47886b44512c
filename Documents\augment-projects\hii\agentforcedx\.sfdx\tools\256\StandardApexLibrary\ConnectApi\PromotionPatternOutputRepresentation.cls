global class PromotionPatternOutputRepresentation {
	global List<ConnectApi.PromotionActionOutputRepresentation> action;
	global List<ConnectApi.PromotionCriteriaGroupOutputRepresentation> criteriaGroup;
	global String id;
	global String name;
	global Integer priority;
	global String type;
	global PromotionPatternOutputRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}