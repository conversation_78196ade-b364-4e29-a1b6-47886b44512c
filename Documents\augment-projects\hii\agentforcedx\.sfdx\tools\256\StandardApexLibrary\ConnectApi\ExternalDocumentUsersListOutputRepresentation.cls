global class ExternalDocumentUsersListOutputRepresentation {
	global String code;
	global String message;
	global List<ConnectApi.ExternalDocumentUsersOutputRepresentation> users;
	global ExternalDocumentUsersListOutputRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}