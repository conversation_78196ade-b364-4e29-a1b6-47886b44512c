global class ExtractionContextAttributeMappingRepresentation {
	global String attributeId;
	global String attributeName;
	global String description;
	global Boolean isCustom;
	global String targetObjectField;
	global ExtractionContextAttributeMappingRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}