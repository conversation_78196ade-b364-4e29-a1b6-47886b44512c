global class EngagementTopicDetailsOutput {
	global String id;
	global String interactionSummary;
	global String name;
	global String ownerId;
	global String parentTopicId;
	global String processFailureReason;
	global String processName;
	global String processStatus;
	global String processType;
	global String relatedPersonId;
	global String topicId;
	global EngagementTopicDetailsOutput() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}