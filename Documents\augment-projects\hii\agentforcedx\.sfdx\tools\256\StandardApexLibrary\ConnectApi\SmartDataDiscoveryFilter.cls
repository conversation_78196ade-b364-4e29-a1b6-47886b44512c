global class SmartDataDiscoveryFilter {
	global String fieldName;
	global List<ConnectApi.SmartDataDiscoveryFilterValue> filterValues;
	global ConnectApi.SmartDataDiscoveryFilterOperator operator;
	global ConnectApi.SmartDataDiscoveryFilterFieldTypeEnum type;
	global List<String> values;
	global SmartDataDiscoveryFilter() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}