global class ExternalManagedAccountOutput {
	global String accountId;
	global String accountName;
	global ConnectApi.ExternalManagedAccountAddressOutput address;
	global String externalManagedAccountId;
	global Boolean isMyAccount;
	global ExternalManagedAccountOutput() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}