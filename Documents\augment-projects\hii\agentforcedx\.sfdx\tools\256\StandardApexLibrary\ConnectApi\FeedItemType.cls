global enum FeedItemType {
ACTIVITYEVENT,
ADVA<PERSON>EDTEXTPOST,
<PERSON><PERSON><PERSON><PERSON><PERSON>MENTPOST,
AP<PERSON><PERSON><PERSON><PERSON>OST,
ATTACHARTICLEEVENT,
BASICTEMPLATEFEEDITEM,
CALL<PERSON>OGPOST,
CA<PERSON><PERSON><PERSON>OST,
CA<PERSON>COMMENTPOST,
CH<PERSON><PERSON>STATUSPOST,
<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>OS<PERSON>,
<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>OUPCREATED,
<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>GROUPUNARCHIVED,
CONTEN<PERSON>OS<PERSON>,
CREATERECORDEVENT,
DASH<PERSON>ARDCOMPONENTALERT,
DASHBOARDCOMPONENTSNAPSHOT,
EMAILMESSAGEEVENT,
FACEBOOKPOST,
LINKPOST,
MILE<PERSON>ONEEVENT,
P<PERSON>LPOST,
PRO<PERSON><PERSON><PERSON>K<PERSON>LPOST,
QUESTIONPOST,
R<PERSON><PERSON><PERSON>OS<PERSON>,
<PERSON><PERSON><PERSON><PERSON>OS<PERSON>,
<PERSON><PERSON><PERSON><PERSON>OS<PERSON>,
TEX<PERSON>OST,
TRACKEDCHANGE,
USERSTATUS
}