global class LearningFoundationItemOutput {
	global String category;
	global Double duration;
	global String durationUnit;
	global List<ConnectApi.LearningFulfiller> fulfilledBy;
	global Double fulfilledDuration;
	global Boolean isFulfilled;
	global String learningAchievementId;
	global String learningAchievementType;
	global String learningFoundationItemId;
	global String learningId;
	global Double minimumNumericGrade;
	global String name;
	global Double remainingDuration;
	global String type;
	global LearningFoundationItemOutput() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}