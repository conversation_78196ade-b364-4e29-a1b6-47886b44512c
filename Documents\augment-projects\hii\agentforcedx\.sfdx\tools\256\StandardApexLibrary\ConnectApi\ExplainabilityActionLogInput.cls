global class ExplainabilityActionLogInput {
	global String actionContextCode;
	global String actionLog;
	global String actionLogDate;
	global String actionLogOwnerId;
	global String additionalFilter;
	global String name;
	global String primaryFilter;
	global String secondaryFilter;
	global String specificationName;
	global ExplainabilityActionLogInput() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Integer hashCode() { }
	global String toString() { }

}