global class CommerceShopperAgentContextOutputRepresentation {
	global Boolean isShopperAgentEnabled;
	global ConnectApi.CommerceShopperAgentContextMIAWConfigOutputRepresentation miawConfiguration;
	global CommerceShopperAgentContextOutputRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}