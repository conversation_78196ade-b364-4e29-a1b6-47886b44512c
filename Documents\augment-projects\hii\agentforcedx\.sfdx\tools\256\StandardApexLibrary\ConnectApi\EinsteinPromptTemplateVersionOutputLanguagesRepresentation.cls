global class EinsteinPromptTemplateVersionOutputLanguagesRepresentation {
	global List<ConnectApi.EinsteinPromptTemplateSupportedLanguageErrorRepresentation> errors;
	global Boolean isSuccess;
	global String promptTemplateDevName;
	global List<ConnectApi.EinsteinPromptTemplateSupportedLanguageRepresentation> supportedLanguages;
	global String versionId;
	global EinsteinPromptTemplateVersionOutputLanguagesRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}