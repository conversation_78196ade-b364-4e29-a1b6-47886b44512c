global class SmartDataDiscoveryPrescribableField {
	global List<ConnectApi.SmartDataDiscoveryCustomPrescribableFieldDefinition> customDefinitions;
	global ConnectApi.AbstractSmartDataDiscoveryModelField field;
	global SmartDataDiscoveryPrescribableField() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}