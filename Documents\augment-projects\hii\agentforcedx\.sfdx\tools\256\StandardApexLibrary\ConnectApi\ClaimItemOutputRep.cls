global class ClaimItemOutputRep {
	global String category;
	global ConnectApi.ClaimItemDetailOutputRep claimItemDetails;
	global String instanceKey;
	global String insuredItemId;
	global String name;
	global String participantInstanceKey;
	global String productCode;
	global ClaimItemOutputRep() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}