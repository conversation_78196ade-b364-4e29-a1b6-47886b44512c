global class GiftCommitmentDefaultDesignationsOutputRepresentation {
	global List<ConnectApi.GiftCommitmentDefaultDesignationRecordOutputRepresentation> commitmentDefaultDesignations;
	global GiftCommitmentDefaultDesignationsOutputRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}