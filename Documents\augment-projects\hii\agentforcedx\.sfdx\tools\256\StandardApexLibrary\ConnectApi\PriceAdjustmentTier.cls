global class PriceAdjustmentTier {
	global ConnectApi.PriceAdjustmentTierType adjustmentType;
	global String adjustmentValue;
	global String id;
	global String lowerBound;
	global String tierUnitPrice;
	global String upperBound;
	global PriceAdjustmentTier() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}