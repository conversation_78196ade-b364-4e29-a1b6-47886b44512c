global class AdQuoteLineCreativeSizeTypeOutputRepresentation {
	global String adCreativeSizeTypeId;
	global String adQuoteLineCreativeSizeTypeId;
	global String adSpaceCreativeSizeTypeId;
	global String adSpaceSpecificationId;
	global List<ConnectApi.CompanionAdQuoteLineCreativeSizeTypeOutputRepresentation> companions;
	global Integer count;
	global Map<String,Object> customFields;
	global String mediaOrientation;
	global AdQuoteLineCreativeSizeTypeOutputRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}