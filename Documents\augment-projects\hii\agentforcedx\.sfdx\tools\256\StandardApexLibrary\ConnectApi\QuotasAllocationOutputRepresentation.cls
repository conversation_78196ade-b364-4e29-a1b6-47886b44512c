global class QuotasAllocationOutputRepresentation {
	global String code;
	global Boolean isSuccess;
	global String message;
	global List<ConnectApi.QuotaAvailabilityRepresentation> quotaAvailability;
	global QuotasAllocationOutputRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}