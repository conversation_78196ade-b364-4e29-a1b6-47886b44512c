global class CPQCategoryOutputRepresentation {
	global String catalogId;
	global Map<String,Object> customFields;
	global String description;
	global Boolean hasSubCategories;
	global String id;
	global Boolean isNavigational;
	global String name;
	global String parentCategoryId;
	global ConnectApi.CPQQualificationContextOutputRepresentation qualificationContext;
	global Integer sortOrder;
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}