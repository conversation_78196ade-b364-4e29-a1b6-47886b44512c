global class ScoringQueryAdditionalScores {
	global Integer accountEngagementScore;
	global Integer accountFitScore;
	global Integer accountIntentScore;
	global Integer accountScore;
	global Integer engagementScore;
	global Integer fitScore;
	global Integer intentScore;
	global ScoringQueryAdditionalScores() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}