global class ReportTypeDetailsRepresentation {
	global String category;
	global String description;
	global String label;
	global String name;
	global List<ConnectApi.ObjectWithJoinRepresentation> objects;
	global ReportTypeDetailsRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}