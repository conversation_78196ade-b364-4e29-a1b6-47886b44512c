global class AdjustOrderSummaryOutputRepresentation {
	global String inFulfillmentChangeOrderId;
	global String postFulfillmentChangeOrderId;
	global String preFulfillmentChangeOrderId;
	global AdjustOrderSummaryOutputRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}