global class FeedItemPage {
	global String currentPageToken;
	global String currentPageUrl;
	global String isModifiedToken;
	global String isModifiedUrl;
	global List<ConnectApi.FeedItem> items;
	global String nextPageToken;
	global String nextPageUrl;
	global String updatesToken;
	global String updatesUrl;
	global FeedItemPage() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}