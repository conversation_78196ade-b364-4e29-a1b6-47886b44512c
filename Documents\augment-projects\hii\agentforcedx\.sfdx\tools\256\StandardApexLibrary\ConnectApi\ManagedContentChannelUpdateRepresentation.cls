global class ManagedContentChannelUpdateRepresentation {
	global Long cacheControlMaxAge;
	global String domain;
	global Boolean isDomainLocked;
	global Boolean isSearchable;
	global Long mediaCacheControlMaxAge;
	global String name;
	global String targetId;
	global ManagedContentChannelUpdateRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Integer hashCode() { }
	global String toString() { }

}