global class CdpCalculatedInsightDimension {
	global String apiName;
	global String creationType;
	global ConnectApi.CdpCalculatedInsightDataSource dataSource;
	global String dataType;
	global String dateGranularity;
	global String displayName;
	global String fieldRole;
	global String formula;
	global CdpCalculatedInsightDimension() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}