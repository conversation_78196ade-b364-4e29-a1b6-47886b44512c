global class TargetingTemplateOutputRepresentation {
	global Map<String,Object> error;
	global String jobId;
	global String jobStatus;
	global String message;
	global Map<String,String> result;
	global TargetingTemplateOutputRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}