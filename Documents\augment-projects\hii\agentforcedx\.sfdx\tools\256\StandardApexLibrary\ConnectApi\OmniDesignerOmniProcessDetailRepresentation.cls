global class OmniDesignerOmniProcessDetailRepresentation {
	global String createdById;
	global String createdByName;
	global String createdDate;
	global String description;
	global String id;
	global Boolean isActive;
	global Boolean isTestProcedure;
	global String lastModifiedById;
	global String lastModifiedByName;
	global String lastModifiedDate;
	global String name;
	global String subType;
	global String type;
	global String versionNumber;
	global OmniDesignerOmniProcessDetailRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}