global class PricingRecipeTableMappingParameterRepresentation {
	global String error;
	global List<ConnectApi.LookupTableParametersOutputRepresentation> lookupTables;
	global Boolean success;
	global PricingRecipeTableMappingParameterRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}