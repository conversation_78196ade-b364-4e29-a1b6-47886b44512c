global class AddOrderItemSummaryOutputRepresentation {
	global ConnectApi.ChangeItemOutputRepresentation changeBalances;
	global String changeOrderId;
	global List<ConnectApi.AddItemOutputRepresentation> newItems;
	global String orderSummaryId;
	global AddOrderItemSummaryOutputRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}