global class CompositeCommerceVariationOutputRepresentation {
	global Map<String,List<ConnectApi.CompositeErrorResponseRepresentation>> errorDetails;
	global List<ConnectApi.ErrorResponse> errors;
	global List<String> productIds;
	global Boolean success;
	global CompositeCommerceVariationOutputRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}