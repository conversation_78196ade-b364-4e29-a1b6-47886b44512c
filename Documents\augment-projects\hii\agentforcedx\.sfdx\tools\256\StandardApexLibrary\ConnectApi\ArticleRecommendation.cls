global class ArticleRecommendation {
	global String generationsId;
	global String knowledgeArticleId;
	global String knowledgeArticleVersionId;
	global Datetime lastPublishedDate;
	global String recommendationId;
	global Double relevanceScore;
	global String relevantSnippet;
	global String summary;
	global String title;
	global String urlName;
	global ArticleRecommendation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}