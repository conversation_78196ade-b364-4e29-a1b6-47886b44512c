global class CdpCalculatedInsightMeasure {
	global String apiName;
	global String creationType;
	global ConnectApi.CdpCalculatedInsightDataSource dataSource;
	global String dataType;
	global String displayName;
	global String fieldAggregationType;
	global String fieldRole;
	global String formula;
	global CdpCalculatedInsightMeasure() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}