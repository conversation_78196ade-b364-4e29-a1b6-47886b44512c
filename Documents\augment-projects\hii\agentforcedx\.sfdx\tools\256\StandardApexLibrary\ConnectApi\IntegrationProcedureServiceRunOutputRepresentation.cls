global class IntegrationProcedureServiceRunOutputRepresentation {
	global String error;
	global List<String> response;
	global ConnectApi.IntegrationProcedureServiceRunStatus status;
	global IntegrationProcedureServiceRunOutputRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}