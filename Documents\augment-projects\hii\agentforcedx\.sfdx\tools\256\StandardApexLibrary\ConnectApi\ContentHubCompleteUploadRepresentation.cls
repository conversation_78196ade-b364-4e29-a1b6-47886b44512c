global class ContentHubCompleteUploadRepresentation {
	global Integer code;
	global String contentDocumentId;
	global String contentDocumentLinkId;
	global String contentVersionId;
	global Boolean isSuccess;
	global String message;
	global ContentHubCompleteUploadRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}