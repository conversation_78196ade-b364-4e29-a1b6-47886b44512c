global class ContentDocumentResponse {
	global String contentDocumentId;
	global String contentDocumentLinkId;
	global String contractDocumentVersionId;
	global String createdDate;
	global String documentType;
	global String fileExtension;
	global Boolean isSharedExternally;
	global String lastModifiedDate;
	global String title;
	global ContentDocumentResponse() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}