global class PartyFinancialLiabilityBorrowerResultRepresentation {
	global String accountId;
	global String applicantId;
	global String contactId;
	global String createdById;
	global Datetime createdDate;
	global List<ConnectApi.CustomFieldsResultRepresentation> customFields;
	global String id;
	global String lastModifiedById;
	global Datetime lastModifiedDate;
	global String name;
	global String ownerId;
	global String partyFinancialLiabilityId;
	global String partyProfileId;
	global String recordTypeId;
	global Double sharePercent;
	global String sourceSystemIdentifier;
	global partyFinancialLiabilityBorrowerResultRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}