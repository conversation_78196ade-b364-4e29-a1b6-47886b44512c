global class OCIBatchUpdateSingleOutputRepresentation {
	global String actionRequestId;
	global String externalRefId;
	global String locationIdentifier;
	global String stockKeepingUnit;
	global OCIBatchUpdateSingleOutputRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}