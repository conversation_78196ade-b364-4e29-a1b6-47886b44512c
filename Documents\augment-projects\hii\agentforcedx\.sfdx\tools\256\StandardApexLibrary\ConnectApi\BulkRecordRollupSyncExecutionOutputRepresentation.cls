global class BulkRecordRollupSyncExecutionOutputRepresentation {
	global List<ConnectApi.RecordRollupSyncExecutionOutputRepresentation> definitions;
	global Boolean errors;
	global String message;
	global String statusCode;
	global BulkRecordRollupSyncExecutionOutputRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}