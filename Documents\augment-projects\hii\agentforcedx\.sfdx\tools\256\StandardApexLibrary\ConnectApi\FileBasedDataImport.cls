global class FileBasedDataImport {
	global String dataFilterType;
	global Integer failedRecordsCount;
	global String failedResults;
	global String fileBasedDataImportId;
	global String jobIdentifier;
	global String lastModifiedDate;
	global String parentJobDataImportId;
	global String sourceFileName;
	global String status;
	global Integer successRecordsCount;
	global String successfulResults;
	global String targetContext;
	global FileBasedDataImport() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}