global class OCIBatchUpdateSingleInputRepresentation {
	global String actionRequestId;
	global ConnectApi.OCIAttributeSetInputRepresentation attributeSet;
	global String effectiveDate;
	global String externalRefId;
	global List<ConnectApi.OCIFutureStockSingleInputRepresentation> futureStock;
	global String locationIdentifier;
	global Double onHand;
	global Double safetyStockCount;
	global String stockKeepingUnit;
	global OCIBatchUpdateSingleInputRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Integer hashCode() { }
	global String toString() { }

}