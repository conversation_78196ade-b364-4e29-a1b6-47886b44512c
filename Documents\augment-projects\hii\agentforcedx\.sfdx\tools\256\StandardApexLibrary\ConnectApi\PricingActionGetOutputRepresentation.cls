global class PricingActionGetOutputRepresentation {
	global ConnectApi.PricingErrorResponse error;
	global String id;
	global List<ConnectApi.PricingActionParamValuesOutputRepresentation> pricingActionParamValues;
	global Boolean success;
	global PricingActionGetOutputRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}