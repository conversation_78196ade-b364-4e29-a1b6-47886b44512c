global class ClaimParticipantOutputRep {
	global String accountId;
	global ConnectApi.ClaimParticipantDetailOutputRep claimParticipantDetails;
	global String contactId;
	global String instanceKey;
	global String insurancePolicyParticipantId;
	global String name;
	global List<String> roles;
	global ClaimParticipantOutputRep() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}