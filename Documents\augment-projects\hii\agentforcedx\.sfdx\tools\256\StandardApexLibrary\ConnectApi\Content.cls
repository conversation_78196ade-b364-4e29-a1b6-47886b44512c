global class Content {
	global String checksum;
	global String contentUrl;
	global String description;
	global String downloadUrl;
	global String fileExtension;
	global String fileSize;
	global String fileType;
	global String fileTypeEnumName;
	global Boolean hasFlashPreview;
	global Boolean hasPdfPreview;
	global Boolean hasTinyThumbnailPreview;
	global String id;
	global Boolean isContextUserSubscribedToDocument;
	global Boolean isInMyFileSync;
	global Boolean isRenditionProcessing;
	global Boolean isSyncAvailable;
	global Datetime lastModifiedDate;
	global String mimeType;
	global String renditionUrl;
	global String renditionUrl240By180;
	global String renditionUrl720By480;
	global ConnectApi.FileSharingOption sharingOption;
	global String textPreview;
	global String thumb120By90RenditionStatus;
	global String thumb240By180RenditionStatus;
	global String thumb720By480RenditionStatus;
	global String title;
	global String versionId;
	global Content() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}