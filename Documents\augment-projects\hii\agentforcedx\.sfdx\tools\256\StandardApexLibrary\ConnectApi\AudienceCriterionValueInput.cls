global class AudienceCriterionValueInput {
	global String audienceId;
	global String city;
	global String country;
	global String domainId;
	global String entityField;
	global String entityType;
	global String fieldValue;
	global Boolean isEnabled;
	global String permission;
	global String profileId;
	global String subdivision;
	global AudienceCriterionValueInput() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Integer hashCode() { }
	global String toString() { }

}