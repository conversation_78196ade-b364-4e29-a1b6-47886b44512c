global class PartyIdentityVerificationStepResultRepresentation {
	global String createdById;
	global Datetime createdDate;
	global List<ConnectApi.CustomFieldsResultRepresentation> customFields;
	global String id;
	global String identityVerificationArtifactId;
	global String lastModifiedById;
	global Datetime lastModifiedDate;
	global String name;
	global String ownerId;
	global String partyIdentityVerificationId;
	global String recordTypeId;
	global String reportUrl;
	global String status;
	global String type;
	global String verificationDecision;
	global PartyIdentityVerificationStepResultRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}