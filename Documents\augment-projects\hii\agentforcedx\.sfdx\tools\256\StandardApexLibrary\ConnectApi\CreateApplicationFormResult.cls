global class CreateApplicationFormResult {
	global String accountId;
	global List<ConnectApi.CreateApplicationFormApplicantOutputRepresentation> applicants;
	global String applicationFormId;
	global List<ConnectApi.CreateApplicationFormProductOutputRepresentation> applicationFormProducts;
	global CreateApplicationFormResult() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}