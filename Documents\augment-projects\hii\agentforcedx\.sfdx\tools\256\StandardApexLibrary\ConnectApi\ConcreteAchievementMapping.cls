global class ConcreteAchievementMapping {
	global ConnectApi.EduExternalIdDetails courseExternalId;
	global Double duration;
	global String durationUnit;
	global Boolean isPrimary;
	global String learningAchievementId;
	global String learningId;
	global String learningOutcomeId;
	global Double minimumGrade;
	global ConnectApi.EduExternalIdDetails programExternalId;
	global ConcreteAchievementMapping() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Integer hashCode() { }
	global String toString() { }

}