global class CommerceEinsteinOutputConfiguration {
	global Boolean activityTrackingEnabled;
	global Boolean catalogExists;
	global Boolean deploymentEnabled;
	global String host;
	global Boolean isDeployed;
	global String siteId;
	global String tenant;
	global CommerceEinsteinOutputConfiguration() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}