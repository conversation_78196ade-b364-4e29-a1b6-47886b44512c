global class RefundResponse {
	global String accountId;
	global Double amount;
	global String currencyIsoCode;
	global Datetime effectiveDate;
	global String id;
	global String refundNumber;
	global Datetime requestDate;
	global String status;
	global RefundResponse() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}