global class CloneSalesTransactionOutputRepresentation {
	global List<ConnectApi.CloneSalesTransactionErrorResponseRepresentation> errors;
	global String requestId;
	global String salesTransactionId;
	global Boolean success;
	global CloneSalesTransactionOutputRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}