global class EinsteinPromptRecordRepresentation {
	global String apiName;
	global Map<String,List<ConnectApi.EinsteinPromptRecordRepresentation>> childRelationships;
	global Map<String,ConnectApi.EinsteinPromptRecordFieldRepresentation> fields;
	global String id;
	global Boolean isStandard;
	global EinsteinPromptRecordRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}