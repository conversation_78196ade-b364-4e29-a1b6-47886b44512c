global class RecommendationAudiencePage {
	global Integer audienceCount;
	global String currentPageUrl;
	global String nextPageUrl;
	global String previousPageUrl;
	global List<ConnectApi.RecommendationAudience> recommendationAudiences;
	global RecommendationAudiencePage() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}