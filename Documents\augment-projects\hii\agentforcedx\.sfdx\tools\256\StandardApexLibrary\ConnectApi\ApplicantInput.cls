global class ApplicantInput {
	global String accountId;
	global String birthDate;
	global String contactId;
	global String currencyIsoCode;
	global List<ConnectApi.CustomFieldsInputRepresentation> customFields;
	global String email;
	global String firstName;
	global Double groupExposure;
	global String lastName;
	global String middleName;
	global String phone;
	global String phoneType;
	global String recordTypeId;
	global String role;
	global String salutation;
	global String stage;
	global String suffix;
	global String uniqueReferenceNumber;
	global applicantInput() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Integer hashCode() { }
	global String toString() { }

}